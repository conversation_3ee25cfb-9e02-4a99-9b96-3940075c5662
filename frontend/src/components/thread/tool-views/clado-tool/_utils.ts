import { extractToolData, normalizeContentToString } from '../utils';

// LinkedIn Profile Interface
export interface LinkedInProfile {
  id: string;
  name: string;
  location?: string;
  headline?: string;
  description?: string;
  title?: string;
  profile_picture_url?: string;
  linkedin_url?: string;
  twitter_handle?: string;
  websites?: Array<{ url: string }>;
  criteria?: Record<string, string>;
}

// LinkedIn Experience Interface
export interface LinkedInExperience {
  title: string;
  company_name: string;
  start_date?: string;
  end_date?: string;
  description?: string;
  location?: string;
  company_logo?: string;
}

// LinkedIn Education Interface
export interface LinkedInEducation {
  degree?: string;
  field_of_study?: string;
  school_name: string;
  school_logo?: string;
  start_date?: string;
  end_date?: string;
  description?: string;
}

// LinkedIn Company Interface
export interface LinkedInCompany {
  id: string;
  company_id?: string;
  company_name: string;
  company_website?: string;
  linkedin_company_description?: string;
  linkedin_industry?: string;
  linkedin_speciality?: string;
  linkedin_headcount?: number;
  criteria?: Record<string, string>;
  yc_description?: string;
  batch?: string;
  status?: string;
}

// Contact Information Interface
export interface ContactInfo {
  email?: string;
  phone?: string;
  confidence?: number;
  type?: string;
}

// Social Media Interface
export interface SocialMedia {
  platform: string;
  url: string;
  username?: string;
}

// Post Reaction Interface
export interface PostReaction {
  name: string;
  profile_url?: string;
  reaction_type: string;
  profile_picture?: string;
  headline?: string;
}

// Deep Research Job Interface
export interface DeepResearchJob {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  message?: string;
  query?: string;
  progress?: {
    stage: string;
    batch_number?: number;
    total_batches?: number;
    profiles_found_so_far?: number;
    current_enriched_count?: number;
    target_limit?: number;
  };
  results?: LinkedInProfile[];
  total?: number;
  enrichment_stats?: {
    enrichment_enabled: boolean;
    total_profiles: number;
    profiles_with_contacts: number;
    enrichment_success_rate: string;
  };
  created_at?: number;
  error?: string;
}

// Clado Tool Data Interface
export interface CladoToolData {
  toolFunction: string;
  query?: string;
  results?: LinkedInProfile[] | LinkedInCompany[];
  total_results?: number;
  results_returned?: number;
  cost?: string;
  profile_data?: LinkedInProfile;
  contacts?: ContactInfo[];
  social_media?: SocialMedia[];
  reactions?: PostReaction[];
  job_data?: DeepResearchJob;
  pagination?: {
    current_page: number;
    total_pages: number;
    total_reactions: number;
    reactions_on_page: number;
  };
  actualIsSuccess: boolean;
  actualToolTimestamp?: string;
  actualAssistantTimestamp?: string;
}

const parseContent = (content: any): any => {
  if (typeof content === 'string') {
    try {
      return JSON.parse(content);
    } catch (e) {
      return content;
    }
  }
  return content;
};

const extractFromNewFormat = (content: any): Partial<CladoToolData> => {
  const parsedContent = parseContent(content);

  if (!parsedContent || typeof parsedContent !== 'object') {
    return {};
  }

  // Handle tool_execution format
  if (parsedContent.tool_execution) {
    const toolExecution = parsedContent.tool_execution;
    const toolResult = toolExecution.tool_result;

    if (toolResult && toolResult.output) {
      try {
        const output = typeof toolResult.output === 'string'
          ? JSON.parse(toolResult.output)
          : toolResult.output;

        return {
          ...output,
          actualIsSuccess: toolResult.success !== false,
        };
      } catch (e) {
        return {
          actualIsSuccess: toolResult.success !== false,
        };
      }
    }
  }

  // Handle direct format
  if (parsedContent.query || parsedContent.results || parsedContent.job_id) {
    return {
      ...parsedContent,
      actualIsSuccess: true,
    };
  }

  return {};
};

const extractFromLegacyFormat = (content: any): Partial<CladoToolData> => {
  const contentStr = normalizeContentToString(content);
  if (!contentStr) return {};

  try {
    // Look for JSON data in the content
    const jsonMatch = contentStr.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0]);
      return {
        ...parsed,
        actualIsSuccess: true,
      };
    }
  } catch (e) {
    // Ignore parsing errors
  }

  return {};
};

export function extractCladoToolData(
  assistantContent: string | object | undefined | null,
  toolContent: string | object | undefined | null,
  isSuccess: boolean,
  toolTimestamp?: string,
  assistantTimestamp?: string
): CladoToolData {
  // Try new format first
  const assistantData = extractFromNewFormat(assistantContent);
  const toolData = extractFromNewFormat(toolContent);

  // Merge data, preferring tool content over assistant content
  let mergedData = { ...assistantData, ...toolData };

  // If no data found, try legacy format
  if (Object.keys(mergedData).length === 0) {
    const assistantLegacy = extractFromLegacyFormat(assistantContent);
    const toolLegacy = extractFromLegacyFormat(toolContent);
    mergedData = { ...assistantLegacy, ...toolLegacy };
  }

  // Determine tool function from the data or content
  let toolFunction = 'unknown';
  if (mergedData.job_id) {
    toolFunction = mergedData.status ? 'get-deep-research-status' : 'start-deep-research';
  } else if (mergedData.contacts) {
    toolFunction = 'get-linkedin-contacts';
  } else if (mergedData.reactions) {
    toolFunction = 'get-linkedin-post-reactions';
  } else if (mergedData.profile_data) {
    // Check if it's scraped profile data (has experience/education/posts) or enriched profile data
    const profileData = mergedData.profile_data;
    if (profileData && (profileData.experience || profileData.education || profileData.posts)) {
      toolFunction = 'scrape-linkedin-profile';
    } else {
      toolFunction = 'enrich-linkedin-profile';
    }
  } else if (mergedData.results) {
    // Check if results contain company data or user data
    const firstResult = Array.isArray(mergedData.results) ? mergedData.results[0] : null;
    if (firstResult && ('company_name' in firstResult || 'linkedin_industry' in firstResult)) {
      toolFunction = 'search-linkedin-companies';
    } else {
      toolFunction = 'search-linkedin-users';
    }
  }

  // Also try to detect from assistant content if available
  const assistantStr = normalizeContentToString(assistantContent);
  if (toolFunction === 'unknown' && assistantStr) {
    if (assistantStr.includes('search_linkedin_users') || assistantStr.includes('search-linkedin-users')) {
      toolFunction = 'search-linkedin-users';
    } else if (assistantStr.includes('search_linkedin_companies') || assistantStr.includes('search-linkedin-companies')) {
      toolFunction = 'search-linkedin-companies';
    } else if (assistantStr.includes('enrich_linkedin_profile') || assistantStr.includes('enrich-linkedin-profile')) {
      toolFunction = 'enrich-linkedin-profile';
    } else if (assistantStr.includes('get_linkedin_contacts') || assistantStr.includes('get-linkedin-contacts')) {
      toolFunction = 'get-linkedin-contacts';
    } else if (assistantStr.includes('scrape_linkedin_profile') || assistantStr.includes('scrape-linkedin-profile')) {
      toolFunction = 'scrape-linkedin-profile';
    } else if (assistantStr.includes('get_linkedin_post_reactions') || assistantStr.includes('get-linkedin-post-reactions')) {
      toolFunction = 'get-linkedin-post-reactions';
    } else if (assistantStr.includes('start_deep_research') || assistantStr.includes('start-deep-research')) {
      toolFunction = 'start-deep-research';
    } else if (assistantStr.includes('get_deep_research_status') || assistantStr.includes('get-deep-research-status')) {
      toolFunction = 'get-deep-research-status';
    }
  }

  return {
    toolFunction,
    query: mergedData.query || '',
    results: mergedData.results || [],
    total_results: mergedData.total_results || mergedData.total || 0,
    results_returned: mergedData.results_returned || (Array.isArray(mergedData.results) ? mergedData.results.length : 0),
    cost: mergedData.cost || '',
    profile_data: mergedData.profile_data,
    contacts: mergedData.contacts || [],
    social_media: mergedData.social_media || [],
    reactions: mergedData.reactions || [],
    job_data: mergedData.job_id ? mergedData as DeepResearchJob : undefined,
    pagination: mergedData.pagination,
    actualIsSuccess: mergedData.actualIsSuccess ?? isSuccess,
    actualToolTimestamp: toolTimestamp,
    actualAssistantTimestamp: assistantTimestamp,
  };
}

export function getCladoToolIcon(toolFunction: string) {
  // This will be used to get appropriate icons for different Clado functions
  // We'll import the icons in the main component
  return 'linkedin'; // Default to LinkedIn icon
}

export function getCladoToolTitle(toolFunction: string): string {
  const titles: Record<string, string> = {
    'search-linkedin-users': 'LinkedIn User Search',
    'search-linkedin-companies': 'LinkedIn Company Search',
    'enrich-linkedin-profile': 'LinkedIn Profile Enrichment',
    'get-linkedin-contacts': 'LinkedIn Contact Information',
    'scrape-linkedin-profile': 'LinkedIn Profile Scraping',
    'get-linkedin-post-reactions': 'LinkedIn Post Reactions',
    'start-deep-research': 'Deep Research Job',
    'get-deep-research-status': 'Deep Research Status',
  };

  return titles[toolFunction] || 'LinkedIn Research';
}
