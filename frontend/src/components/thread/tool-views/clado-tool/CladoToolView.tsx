import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import {
  Linkedin,
  Users,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ExternalLink,
  Heart,
  MessageCircle,
  Share,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { ToolViewProps } from '../types';
import { formatTimestamp } from '../utils';
import { LoadingState } from '../shared/LoadingState';
import {
  extractCladoToolData,
  getCladoToolTitle,
  CladoToolData,
  LinkedInProfile,
  LinkedInCompany,
  ContactInfo,
  PostReaction,
  DeepResearchJob,
  LinkedInExperience,
  LinkedInEducation
} from './_utils';

export function CladoToolView({
  name = 'search-linkedin-users',
  assistant<PERSON>ontent,
  toolContent,
  assistantTimestamp,
  toolTimestamp,
  isSuccess = true,
  isStreaming = false,
}: ToolViewProps) {
  const [progress, setProgress] = useState(0);
  const [expandedProfiles, setExpandedProfiles] = useState<Record<number, boolean>>({});

  const cladoData = extractCladoToolData(
    assistantContent,
    toolContent,
    isSuccess,
    toolTimestamp,
    assistantTimestamp
  );

  const toolTitle = getCladoToolTitle(cladoData.toolFunction);

  const toggleProfileExpand = (index: number) => {
    setExpandedProfiles(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const renderProfileCard = (profile: LinkedInProfile, index: number) => {
    const isExpanded = expandedProfiles[index];

    return (
      <Card key={profile.id || index} className="mb-4 border border-zinc-200 dark:border-zinc-800">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              {profile.profile_picture_url ? (
                <img
                  src={profile.profile_picture_url}
                  alt={profile.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                  <Users className="w-6 h-6 text-zinc-500" />
                </div>
              )}
              <div className="flex-1">
                <CardTitle className="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                  {profile.name}
                </CardTitle>
                {profile.headline && (
                  <p className="text-sm text-zinc-600 dark:text-zinc-400 mt-1">
                    {profile.headline}
                  </p>
                )}
                {profile.location && (
                  <div className="flex items-center mt-1 text-xs text-zinc-500">
                    <MapPin className="w-3 h-3 mr-1" />
                    {profile.location}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {profile.linkedin_url && (
                <a
                  href={profile.linkedin_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <ExternalLink className="w-4 h-4" />
                </a>
              )}
              <button
                onClick={() => toggleProfileExpand(index)}
                className="text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-300"
              >
                {isExpanded ? 'Less' : 'More'}
              </button>
            </div>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0">
            {profile.description && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">About</h4>
                <p className="text-sm text-zinc-600 dark:text-zinc-400 leading-relaxed">
                  {profile.description}
                </p>
              </div>
            )}

            {profile.criteria && Object.keys(profile.criteria).length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Match Criteria</h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(profile.criteria).map(([criterion, match]) => (
                    <Badge
                      key={criterion}
                      variant={match === 'YES' ? 'default' : match === 'MAYBE' ? 'secondary' : 'outline'}
                      className="text-xs"
                    >
                      {criterion}: {match}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {profile.websites && profile.websites.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Websites</h4>
                <div className="space-y-1">
                  {profile.websites.map((website, idx) => (
                    <a
                      key={idx}
                      href={website.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      {website.url}
                    </a>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        )}
      </Card>
    );
  };

  const renderCompanyCard = (company: LinkedInCompany, index: number) => {
    return (
      <Card key={company.id || index} className="mb-4 border border-zinc-200 dark:border-zinc-800">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-lg bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                <Building2 className="w-6 h-6 text-zinc-500" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                  {company.company_name}
                </CardTitle>
                {company.linkedin_industry && (
                  <p className="text-sm text-zinc-600 dark:text-zinc-400 mt-1">
                    {company.linkedin_industry}
                  </p>
                )}
                {company.linkedin_headcount && (
                  <div className="flex items-center mt-1 text-xs text-zinc-500">
                    <Users className="w-3 h-3 mr-1" />
                    {company.linkedin_headcount} employees
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {company.company_website && (
                <a
                  href={company.company_website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  <ExternalLink className="w-4 h-4" />
                </a>
              )}
              {company.batch && (
                <Badge variant="outline" className="text-xs">
                  YC {company.batch}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {company.linkedin_company_description && (
            <div className="mb-4">
              <p className="text-sm text-zinc-600 dark:text-zinc-400 leading-relaxed">
                {company.linkedin_company_description}
              </p>
            </div>
          )}

          {company.linkedin_speciality && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Specialties</h4>
              <p className="text-sm text-zinc-600 dark:text-zinc-400">
                {company.linkedin_speciality}
              </p>
            </div>
          )}

          {company.criteria && Object.keys(company.criteria).length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-2">Match Criteria</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(company.criteria).map(([criterion, match]) => (
                  <Badge
                    key={criterion}
                    variant={match === 'YES' ? 'default' : match === 'MAYBE' ? 'secondary' : 'outline'}
                    className="text-xs"
                  >
                    {criterion}: {match}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderContactInfo = (contacts: ContactInfo[]) => {
    return (
      <div className="space-y-3">
        {contacts.map((contact, index) => (
          <Card key={index} className="border border-zinc-200 dark:border-zinc-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {contact.email && (
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4 text-zinc-500" />
                      <span className="text-sm font-medium">{contact.email}</span>
                    </div>
                  )}
                  {contact.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-zinc-500" />
                      <span className="text-sm font-medium">{contact.phone}</span>
                    </div>
                  )}
                </div>
                {contact.confidence && (
                  <Badge variant="outline" className="text-xs">
                    {contact.confidence}% confidence
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderExperience = (experience: LinkedInExperience[]) => {
    return (
      <div className="space-y-3">
        {experience.map((exp, index) => (
          <Card key={index} className="border border-zinc-200 dark:border-zinc-800">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                {exp.company_logo ? (
                  <img
                    src={exp.company_logo}
                    alt={exp.company_name}
                    className="w-10 h-10 rounded object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                    <Building2 className="w-5 h-5 text-zinc-500" />
                  </div>
                )}
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                    {exp.title}
                  </h4>
                  <p className="text-sm text-zinc-600 dark:text-zinc-400">
                    {exp.company_name}
                  </p>
                  <div className="flex items-center mt-1 text-xs text-zinc-500">
                    <Calendar className="w-3 h-3 mr-1" />
                    {exp.start_date && new Date(exp.start_date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                    {exp.end_date && exp.end_date !== '1970-01-01T00:00:00'
                      ? ` - ${new Date(exp.end_date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`
                      : ' - Present'
                    }
                  </div>
                  {exp.location && (
                    <div className="flex items-center mt-1 text-xs text-zinc-500">
                      <MapPin className="w-3 h-3 mr-1" />
                      {exp.location}
                    </div>
                  )}
                  {exp.description && (
                    <p className="text-xs text-zinc-600 dark:text-zinc-400 mt-2 leading-relaxed">
                      {exp.description}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const renderEducation = (education: LinkedInEducation[]) => {
    return (
      <div className="space-y-3">
        {education.map((edu, index) => (
          <Card key={index} className="border border-zinc-200 dark:border-zinc-800">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                {edu.school_logo ? (
                  <img
                    src={edu.school_logo}
                    alt={edu.school_name}
                    className="w-10 h-10 rounded object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                    <Building2 className="w-5 h-5 text-zinc-500" />
                  </div>
                )}
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                    {edu.school_name}
                  </h4>
                  {edu.degree && (
                    <p className="text-sm text-zinc-600 dark:text-zinc-400">
                      {edu.degree}{edu.field_of_study && `, ${edu.field_of_study}`}
                    </p>
                  )}
                  {(edu.start_date || edu.end_date) && (
                    <div className="flex items-center mt-1 text-xs text-zinc-500">
                      <Calendar className="w-3 h-3 mr-1" />
                      {edu.start_date && new Date(edu.start_date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                      {edu.end_date && edu.end_date !== '1970-01-01T00:00:00'
                        ? ` - ${new Date(edu.end_date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`
                        : edu.start_date ? ' - Present' : ''
                      }
                    </div>
                  )}
                  {edu.description && (
                    <p className="text-xs text-zinc-600 dark:text-zinc-400 mt-2 leading-relaxed">
                      {edu.description}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <Card className="gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-white dark:bg-zinc-950">
      <CardHeader className="h-14 bg-blue-50/80 dark:bg-blue-900/20 backdrop-blur-sm border-b p-2 px-4 space-y-2">
        <div className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative p-2 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20">
              <Linkedin className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-base font-medium text-zinc-900 dark:text-zinc-100">
                {toolTitle}
              </CardTitle>
              {cladoData.query && (
                <p className="text-xs text-zinc-600 dark:text-zinc-400 mt-0.5">
                  Query: {cladoData.query}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {cladoData.actualIsSuccess ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
            {isStreaming && (
              <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0 h-full flex-1 overflow-hidden relative">
        {isStreaming ? (
          <LoadingState
            icon={Linkedin}
            iconColor="text-blue-600 dark:text-blue-400"
            bgColor="bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20"
            title="Processing LinkedIn research"
            filePath={cladoData.query || 'LinkedIn data'}
            showProgress={true}
          />
        ) : (
          <ScrollArea className="h-full w-full">
            <div className="p-4 space-y-4">
              {/* Results Summary */}
              {(cladoData.total_results || cladoData.results_returned) && (
                <div className="bg-zinc-50 dark:bg-zinc-900/50 rounded-lg p-3 border border-zinc-200 dark:border-zinc-800">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-zinc-600 dark:text-zinc-400">
                      Found {cladoData.total_results || 0} results, showing {cladoData.results_returned || 0}
                    </span>
                    {cladoData.cost && (
                      <Badge variant="outline" className="text-xs">
                        {cladoData.cost}
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Render content based on tool function */}
              {cladoData.toolFunction === 'search-linkedin-users' && cladoData.results && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    LinkedIn Profiles ({cladoData.results.length})
                  </h3>
                  {(cladoData.results as LinkedInProfile[]).map((profile, index) =>
                    renderProfileCard(profile, index)
                  )}
                </div>
              )}

              {cladoData.toolFunction === 'search-linkedin-companies' && cladoData.results && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Building2 className="w-5 h-5 mr-2" />
                    Companies ({cladoData.results.length})
                  </h3>
                  {(cladoData.results as LinkedInCompany[]).map((company, index) =>
                    renderCompanyCard(company, index)
                  )}
                </div>
              )}

              {cladoData.toolFunction === 'get-linkedin-contacts' && cladoData.contacts && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Mail className="w-5 h-5 mr-2" />
                    Contact Information ({cladoData.contacts.length})
                  </h3>
                  {renderContactInfo(cladoData.contacts)}
                </div>
              )}

              {cladoData.toolFunction === 'scrape-linkedin-profile' && cladoData.profile_data && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    Complete Profile Data
                  </h3>

                  {/* Profile Summary */}
                  {renderProfileCard(cladoData.profile_data, 0)}

                  {/* Experience Section */}
                  {(cladoData.profile_data as any).experience && (cladoData.profile_data as any).experience.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-md font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
                        <Building2 className="w-4 h-4 mr-2" />
                        Experience ({(cladoData.profile_data as any).experience.length})
                      </h4>
                      {renderExperience((cladoData.profile_data as any).experience)}
                    </div>
                  )}

                  {/* Education Section */}
                  {(cladoData.profile_data as any).education && (cladoData.profile_data as any).education.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-md font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        Education ({(cladoData.profile_data as any).education.length})
                      </h4>
                      {renderEducation((cladoData.profile_data as any).education)}
                    </div>
                  )}

                  {/* Posts Section */}
                  {(cladoData.profile_data as any).posts && (cladoData.profile_data as any).posts.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="text-md font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Recent Posts ({(cladoData.profile_data as any).posts.length})
                      </h4>
                      <div className="space-y-3">
                        {(cladoData.profile_data as any).posts.slice(0, 3).map((post: any, index: number) => (
                          <Card key={index} className="border border-zinc-200 dark:border-zinc-800">
                            <CardContent className="p-4">
                              <div className="space-y-2">
                                {post.text && (
                                  <p className="text-sm text-zinc-700 dark:text-zinc-300 leading-relaxed">
                                    {post.text.length > 200 ? `${post.text.substring(0, 200)}...` : post.text}
                                  </p>
                                )}
                                {post.date && (
                                  <div className="flex items-center text-xs text-zinc-500">
                                    <Clock className="w-3 h-3 mr-1" />
                                    {new Date(post.date).toLocaleDateString()}
                                  </div>
                                )}
                                {(post.likes || post.comments || post.shares) && (
                                  <div className="flex items-center space-x-4 text-xs text-zinc-500">
                                    {post.likes && (
                                      <div className="flex items-center">
                                        <Heart className="w-3 h-3 mr-1" />
                                        {post.likes} likes
                                      </div>
                                    )}
                                    {post.comments && (
                                      <div className="flex items-center">
                                        <MessageCircle className="w-3 h-3 mr-1" />
                                        {post.comments} comments
                                      </div>
                                    )}
                                    {post.shares && (
                                      <div className="flex items-center">
                                        <Share className="w-3 h-3 mr-1" />
                                        {post.shares} shares
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                        {(cladoData.profile_data as any).posts.length > 3 && (
                          <p className="text-xs text-zinc-500 dark:text-zinc-400 text-center py-2">
                            ... and {(cladoData.profile_data as any).posts.length - 3} more posts
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {cladoData.toolFunction === 'enrich-linkedin-profile' && cladoData.profile_data && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Users className="w-5 h-5 mr-2" />
                    Profile Details
                  </h3>
                  {renderProfileCard(cladoData.profile_data, 0)}
                </div>
              )}

              {cladoData.toolFunction === 'get-linkedin-post-reactions' && cladoData.reactions && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Heart className="w-5 h-5 mr-2" />
                    Post Reactions ({cladoData.reactions.length})
                  </h3>
                  {cladoData.pagination && (
                    <div className="bg-zinc-50 dark:bg-zinc-900/50 rounded-lg p-3 border border-zinc-200 dark:border-zinc-800">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-zinc-600 dark:text-zinc-400">
                          Page {cladoData.pagination.current_page} of {cladoData.pagination.total_pages}
                        </span>
                        <span className="text-zinc-600 dark:text-zinc-400">
                          {cladoData.pagination.total_reactions} total reactions
                        </span>
                      </div>
                    </div>
                  )}
                  <div className="grid gap-3">
                    {cladoData.reactions.map((reaction, index) => (
                      <Card key={index} className="border border-zinc-200 dark:border-zinc-800">
                        <CardContent className="p-3">
                          <div className="flex items-center space-x-3">
                            {reaction.profile_picture ? (
                              <img
                                src={reaction.profile_picture}
                                alt={reaction.name}
                                className="w-8 h-8 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-8 h-8 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                                <Users className="w-4 h-4 text-zinc-500" />
                              </div>
                            )}
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                  {reaction.name}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {reaction.reaction_type}
                                </Badge>
                              </div>
                              {reaction.headline && (
                                <p className="text-xs text-zinc-600 dark:text-zinc-400 mt-1">
                                  {reaction.headline}
                                </p>
                              )}
                            </div>
                            {reaction.profile_url && (
                              <a
                                href={reaction.profile_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                              >
                                <ExternalLink className="w-4 h-4" />
                              </a>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {(cladoData.toolFunction === 'start-deep-research' || cladoData.toolFunction === 'get-deep-research-status') && cladoData.job_data && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-zinc-900 dark:text-zinc-100 flex items-center">
                    <Clock className="w-5 h-5 mr-2" />
                    Deep Research Job
                  </h3>

                  <Card className="border border-zinc-200 dark:border-zinc-800">
                    <CardContent className="p-4">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                              Job ID: {cladoData.job_data.job_id}
                            </p>
                            {cladoData.job_data.query && (
                              <p className="text-xs text-zinc-600 dark:text-zinc-400 mt-1">
                                Query: {cladoData.job_data.query}
                              </p>
                            )}
                          </div>
                          <Badge
                            variant={
                              cladoData.job_data.status === 'completed' ? 'default' :
                              cladoData.job_data.status === 'error' ? 'destructive' :
                              cladoData.job_data.status === 'processing' ? 'secondary' : 'outline'
                            }
                          >
                            {cladoData.job_data.status}
                          </Badge>
                        </div>

                        {cladoData.job_data.message && (
                          <p className="text-sm text-zinc-600 dark:text-zinc-400">
                            {cladoData.job_data.message}
                          </p>
                        )}

                        {cladoData.job_data.progress && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-xs text-zinc-600 dark:text-zinc-400">
                              <span>Progress: {cladoData.job_data.progress.stage}</span>
                              {cladoData.job_data.progress.batch_number && cladoData.job_data.progress.total_batches && (
                                <span>
                                  Batch {cladoData.job_data.progress.batch_number} of {cladoData.job_data.progress.total_batches}
                                </span>
                              )}
                            </div>
                            {cladoData.job_data.progress.profiles_found_so_far && (
                              <p className="text-xs text-zinc-600 dark:text-zinc-400">
                                Found {cladoData.job_data.progress.profiles_found_so_far} profiles so far
                              </p>
                            )}
                          </div>
                        )}

                        {cladoData.job_data.status === 'completed' && cladoData.job_data.results && (
                          <div className="mt-4 space-y-3">
                            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
                              <div className="flex items-center justify-between text-sm">
                                <span className="text-green-700 dark:text-green-300">
                                  Research completed: {cladoData.job_data.total} results found
                                </span>
                                {cladoData.job_data.enrichment_stats && (
                                  <span className="text-green-600 dark:text-green-400 text-xs">
                                    {cladoData.job_data.enrichment_stats.enrichment_success_rate} enriched
                                  </span>
                                )}
                              </div>
                            </div>

                            <div className="space-y-3">
                              <h4 className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                Research Results ({cladoData.job_data.results.length})
                              </h4>
                              {cladoData.job_data.results.slice(0, 5).map((profile, index) =>
                                renderProfileCard(profile, index)
                              )}
                              {cladoData.job_data.results.length > 5 && (
                                <p className="text-xs text-zinc-500 dark:text-zinc-400 text-center py-2">
                                  ... and {cladoData.job_data.results.length - 5} more results
                                </p>
                              )}
                            </div>
                          </div>
                        )}

                        {cladoData.job_data.status === 'error' && cladoData.job_data.error && (
                          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border border-red-200 dark:border-red-800">
                            <p className="text-sm text-red-700 dark:text-red-300">
                              Error: {cladoData.job_data.error}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </ScrollArea>
        )}
      </CardContent>

      <div className="px-4 py-2 h-10 bg-gradient-to-r from-blue-50/90 to-blue-100/90 dark:from-blue-900/20 dark:to-blue-800/20 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4">
        <div className="h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400">
          <Badge variant="outline" className="h-6 py-0.5 bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800">
            <Linkedin className="h-3 w-3 mr-1" />
            LinkedIn Research
          </Badge>
        </div>

        <div className="text-xs text-zinc-500 dark:text-zinc-400">
          {cladoData.actualToolTimestamp && !isStreaming
            ? formatTimestamp(cladoData.actualToolTimestamp)
            : cladoData.actualAssistantTimestamp
              ? formatTimestamp(cladoData.actualAssistantTimestamp)
              : ""}
        </div>
      </div>
    </Card>
  );
}
